/**
 * SECURE ADMIN CREATION SCRIPT
 *
 * Enterprise-grade script that uses environment variables securely
 * to create admin collections in Firestore.
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

// Validate environment variables
function validateEnvironment() {
  const required = [
    'FIREBASE_ADMIN_PROJECT_ID',
    'FIREBASE_ADMIN_PRIVATE_KEY',
    'FIREBASE_ADMIN_CLIENT_EMAIL'
  ];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(key => console.error(`   - ${key}`));
    console.error('\nPlease check your .env.local file.');
    process.exit(1);
  }

  console.log('✅ Environment variables validated');
}

// Secure Firebase initialization
function initializeFirebase() {
  try {
    validateEnvironment();

    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
      private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
    });

    console.log('✅ Firebase Admin SDK initialized securely');
    return true;
  } catch (error) {
    console.error('❌ Firebase initialization failed:', error.message);
    return false;
  }
}

// Initialize Firebase first
if (!initializeFirebase()) {
  process.exit(1);
}

const db = admin.firestore();
const auth = admin.auth();

async function createAdminSystemSecurely() {
  console.log('🚀 CREATING ADMIN SYSTEM SECURELY...');
  console.log('📊 Project:', process.env.FIREBASE_ADMIN_PROJECT_ID);
  
  try {
    // 1. CREATE ADMIN ROLES
    console.log('Creating admin_roles collection...');
    await db.collection('admin_roles').doc('super-admin').set({
      id: 'super-admin',
      name: 'super-admin',
      displayName: 'Super Admin',
      description: 'Has full access to all system features',
      permissions: ['*'],
      level: 10,
      isSystemRole: true,
      userCount: 0,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system'
    });

    await db.collection('admin_roles').doc('admin').set({
      id: 'admin',
      name: 'admin', 
      displayName: 'Administrator',
      description: 'Can manage most system settings',
      permissions: ['users.view', 'users.create', 'content.view', 'content.create', 'content.edit'],
      level: 8,
      isSystemRole: true,
      userCount: 0,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system'
    });

    await db.collection('admin_roles').doc('editor').set({
      id: 'editor',
      name: 'editor',
      displayName: 'Editor', 
      description: 'Can create and edit content',
      permissions: ['content.view', 'content.create', 'content.edit'],
      level: 4,
      isSystemRole: true,
      userCount: 0,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system'
    });
    console.log('✅ admin_roles collection created!');

    // 2. CREATE ADMIN PERMISSIONS
    console.log('Creating admin_permissions collection...');
    const permissions = [
      'content.view', 'content.create', 'content.edit', 'content.delete', 'content.publish',
      'media.view', 'media.upload', 'media.delete',
      'users.view', 'users.create', 'users.edit', 'users.delete',
      'settings.view', 'settings.edit',
      'analytics.view', 'analytics.export'
    ];

    for (const perm of permissions) {
      await db.collection('admin_permissions').doc(perm).set({
        id: perm,
        name: perm,
        displayName: perm.replace('.', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        description: `Permission to ${perm}`,
        category: perm.split('.')[0],
        resource: perm.split('.')[0],
        action: perm.split('.')[1],
        isSystemPermission: true,
        isActive: true,
        roleCount: 0,
        userCount: 0,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });
    }
    console.log('✅ admin_permissions collection created!');

    // 3. CREATE SUPER ADMIN USER
    console.log('Creating super admin user...');
    const adminEmail = '<EMAIL>';
    const adminPassword = 'AdminPassword123!';
    
    let userRecord;
    try {
      userRecord = await auth.createUser({
        email: adminEmail,
        password: adminPassword,
        displayName: 'Super Administrator',
        emailVerified: true
      });
      console.log('✅ Firebase Auth user created!');
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        userRecord = await auth.getUserByEmail(adminEmail);
        console.log('✅ Using existing Firebase Auth user!');
      } else {
        throw error;
      }
    }

    // Set custom claims
    await auth.setCustomUserClaims(userRecord.uid, {
      admin: true,
      superAdmin: true,
      role: 'super-admin'
    });

    // 4. CREATE ADMIN USER DOCUMENT
    console.log('Creating admin_users collection...');
    await db.collection('admin_users').doc(userRecord.uid).set({
      id: userRecord.uid,
      email: adminEmail,
      displayName: 'Super Administrator',
      authProvider: 'email',
      emailVerified: true,
      roleRef: 'admin_roles/super-admin',
      roleName: 'Super Admin',
      permissions: ['*'],
      isActive: true,
      isSuperAdmin: true,
      firstName: 'Super',
      lastName: 'Administrator',
      title: 'Super Administrator',
      cmsPermissions: {
        content: { canCreate: true, canEdit: true, canDelete: true, canPublish: true, canSchedule: true },
        media: { canUpload: true, canDelete: true, canOrganize: true, maxUploadSize: 100 },
        users: { canView: true, canCreate: true, canEdit: true, canDelete: true, canChangeRoles: true },
        analytics: { canView: true, canExport: true, canConfigureDashboards: true },
        settings: { canViewSystem: true, canEditSystem: true, canManageIntegrations: true, canManageBackups: true }
      },
      workflowPermissions: {
        canApproveContent: true,
        canRejectContent: true,
        canAssignTasks: true,
        canCreateWorkflows: true,
        approvalLevel: 5
      },
      accessRestrictions: {
        requireMFA: false,
        sessionTimeout: 60,
        maxConcurrentSessions: 3
      },
      loginCount: 0,
      failedLoginAttempts: 0,
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        dashboardLayout: 'grid',
        notificationsEnabled: true,
        emailNotifications: true
      },
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system',
      updatedBy: 'system',
      version: 1,
      isDeleted: false
    });
    console.log('✅ admin_users collection created!');

    // 5. CREATE ADMIN PREFERENCES
    console.log('Creating admin_preferences collection...');
    await db.collection('admin_preferences').doc(userRecord.uid).set({
      userId: userRecord.uid,
      theme: 'light',
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      dashboardLayout: 'grid',
      notificationsEnabled: true,
      emailNotifications: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });
    console.log('✅ admin_preferences collection created!');

    // 6. CREATE AUDIT LOG
    console.log('Creating admin_audit_logs collection...');
    await db.collection('admin_audit_logs').add({
      adminRef: `admin_users/system`,
      adminEmail: 'system',
      adminName: 'System',
      action: 'system.initialize',
      resource: 'admin_system',
      description: 'Admin system initialized successfully',
      ipAddress: '0.0.0.0',
      userAgent: 'Setup Script',
      timestamp: admin.firestore.Timestamp.now(),
      severity: 'high',
      category: 'system',
      success: true,
      searchKeywords: ['system', 'initialize', 'admin'],
      dateString: new Date().toISOString().split('T')[0]
    });
    console.log('✅ admin_audit_logs collection created!');

    // 7. CREATE SYSTEM CONFIG
    console.log('Creating system_config collection...');
    await db.collection('system_config').doc('admin_system').set({
      initialized: true,
      initializedAt: admin.firestore.Timestamp.now(),
      initializedBy: userRecord.uid,
      version: '1.0.0',
      features: {
        roleBasedAccess: true,
        auditLogging: true,
        sessionManagement: true,
        permissionInheritance: true,
        multiFactorAuth: false
      }
    });
    console.log('✅ system_config collection created!');

    console.log('');
    console.log('🎉🎉🎉 SUCCESS! ADMIN SYSTEM CREATED! 🎉🎉🎉');
    console.log('');
    console.log('📊 COLLECTIONS CREATED:');
    console.log('✅ admin_users (1 document)');
    console.log('✅ admin_roles (3 documents)'); 
    console.log('✅ admin_permissions (15 documents)');
    console.log('✅ admin_preferences (1 document)');
    console.log('✅ admin_audit_logs (1 document)');
    console.log('✅ system_config (1 document)');
    console.log('');
    console.log('🔐 LOGIN CREDENTIALS:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: AdminPassword123!');
    console.log('🆔 UID:', userRecord.uid);
    console.log('');
    console.log('🔍 CHECK YOUR FIREBASE CONSOLE NOW!');
    console.log('Go to: https://console.firebase.google.com/project/encreasl-daa43/firestore');

  } catch (error) {
    console.error('❌ ERROR:', error);
  } finally {
    process.exit(0);
  }
}

// Execute the secure admin creation
createAdminSystemSecurely();
